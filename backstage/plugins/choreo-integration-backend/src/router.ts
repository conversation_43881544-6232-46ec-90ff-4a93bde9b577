import { HttpAuthService } from '@backstage/backend-plugin-api';
import express from 'express';
import Router from 'express-promise-router';
import { ChoreoApiService } from './services/ChoreoApiService/types';

export async function createRouter({
  httpAuth,
  choreoApiService,
}: {
  httpAuth: HttpAuthService;
  choreoApiService: ChoreoApiService;
}): Promise<express.Router> {
  const router = Router();
  router.use(express.json());



  // Choreo API endpoints
  router.get('/choreo-orgs', async (req, res) => {
    try {
      const credentials = await httpAuth.credentials(req, { allow: ['user'] });
      const organizations = await choreoApiService.getOrganizations({
        credentials,
      });

      res.json({
        organizations,
        status: 'success',
        count: organizations.length,
      });
    } catch (error) {
      res.status(500).json({
        error: 'Failed to fetch Choreo organizations',
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  });

  router.post('/choreo-projects', async (req, res) => {
    try {
      const credentials = await httpAuth.credentials(req, { allow: ['user'] });
      const { orgId, orgHandler } = req.body;

      if (!orgId || !orgHandler) {
        return res.status(400).json({
          error: 'Missing required parameters: orgId and orgHandler',
          status: 'error',
        });
      }

      const projects = await choreoApiService.getProjects({
        credentials,
        orgId: Number(orgId),
        orgHandler: String(orgHandler),
      });

      res.json({
        projects,
        status: 'success',
        count: projects.length,
      });
    } catch (error) {
      res.status(500).json({
        error: 'Failed to fetch Choreo projects',
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  });

  router.post('/choreo-project-creation-eligibility', async (req, res) => {
    try {
      const credentials = await httpAuth.credentials(req, { allow: ['user'] });
      const { orgId, orgHandler } = req.body;

      if (!orgId || !orgHandler) {
        return res.status(400).json({
          error: 'Missing required parameters: orgId and orgHandler',
          status: 'error',
        });
      }

      const eligibility = await choreoApiService.getProjectCreationEligibility({
        credentials,
        orgId: Number(orgId),
        orgHandler: String(orgHandler),
      });

      res.json({
        projectCreationEligibility: eligibility,
        status: 'success',
      });
    } catch (error) {
      res.status(500).json({
        error: 'Failed to check project creation eligibility',
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  });

  router.post('/choreo-components', async (req, res) => {
    try {
      const credentials = await httpAuth.credentials(req, { allow: ['user'] });
      const { orgHandler, projectId } = req.body;

      if (!orgHandler || !projectId) {
        return res.status(400).json({
          error: 'Missing required parameters: orgHandler and projectId',
          status: 'error',
        });
      }

      const components = await choreoApiService.getComponents({
        credentials,
        orgHandler: String(orgHandler),
        projectId: String(projectId),
      });

      res.json({
        components,
        status: 'success',
        count: components.length,
      });
    } catch (error) {
      res.status(500).json({
        error: 'Failed to fetch Choreo components',
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  });

  router.post('/choreo-environments', async (req, res) => {
    try {
      const credentials = await httpAuth.credentials(req, { allow: ['user'] });
      const { orgUuid, projectId, type } = req.body;

      if (!orgUuid || !projectId) {
        return res.status(400).json({
          error: 'Missing required parameters: orgUuid and projectId',
          status: 'error',
        });
      }

      const environments = await choreoApiService.getEnvironments({
        credentials,
        orgUuid: String(orgUuid),
        projectId: String(projectId),
        type: type ? String(type) : 'external',
      });

      res.json({
        environments,
        status: 'success',
        count: environments.length,
      });
    } catch (error) {
      res.status(500).json({
        error: 'Failed to fetch Choreo environments',
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  });

  router.post('/choreo-create-component', async (req, res) => {
    try {
      const credentials = await httpAuth.credentials(req, { allow: ['user'] });
      const componentData = req.body;

      // Validate required fields
      const requiredFields = [
        'repositoryUrl', 'branch', 'displayName', 'componentName',
        'buildPreset', 'orgHandler', 'projectId'
      ];

      const missingFields = requiredFields.filter(field => !componentData[field]);
      if (missingFields.length > 0) {
        return res.status(400).json({
          error: `Missing required fields: ${missingFields.join(', ')}`,
          status: 'error',
        });
      }

      const result = await choreoApiService.createWebAppComponent({
        credentials,
        data: componentData,
      });

      res.json({
        component: result,
        status: 'success',
        message: 'Web app component created successfully',
      });
    } catch (error) {
      res.status(500).json({
        error: 'Failed to create web app component',
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  });

  return router;
}
