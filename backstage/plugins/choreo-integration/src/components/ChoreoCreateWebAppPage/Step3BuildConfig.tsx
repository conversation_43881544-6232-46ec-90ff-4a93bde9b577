import React from 'react';
import {
  Box,
  TextField,
  Typography,
  FormControl,
  FormLabel,
  RadioGroup,
  FormControlLabel,
  Radio,
  Grid,
  Card,
  CardContent,
  Tooltip,
  IconButton,
  FormHelperText,
} from '@material-ui/core';
import { Help as HelpIcon } from '@material-ui/icons';
import { WebAppCreationData, WebAppBuildConfig } from '../../api';
import { BUILD_PRESETS, getPresetById } from './buildPresets';
import { ValidationError } from './validation';

interface Step3BuildConfigProps {
  data: Partial<WebAppCreationData>;
  onChange: (field: keyof WebAppCreationData, value: any) => void;
  onBuildConfigChange: (config: Partial<WebAppBuildConfig>) => void;
  errors: ValidationError[];
}

export const Step3BuildConfig: React.FC<Step3BuildConfigProps> = ({
  data,
  onChange,
  onBuildConfigChange,
  errors,
}) => {
  const getFieldError = (field: string): string | undefined => {
    return errors.find(error => error.field === field)?.message;
  };

  const selectedPreset = getPresetById(data.buildPreset || '');
  const buildConfig = data.buildConfig || {};

  const handlePresetChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const presetId = event.target.value;
    const preset = getPresetById(presetId);
    
    onChange('buildPreset', presetId);
    
    if (preset) {
      // Reset build config to preset defaults
      onBuildConfigChange(preset.defaultConfig);
    }
  };

  const handleConfigChange = (field: keyof WebAppBuildConfig, value: any) => {
    onBuildConfigChange({
      ...buildConfig,
      [field]: value,
    });
  };

  const renderConfigFields = () => {
    if (!selectedPreset) return null;

    const preset = selectedPreset;
    const config = buildConfig;

    return (
      <Box mt={3}>
        <Typography variant="h6" gutterBottom>
          {preset.name} Configuration
        </Typography>
        
        {/* React/Angular specific fields */}
        {['react', 'angular'].includes(preset.id) && (
          <>
            <Box mb={2}>
              <Box display="flex" alignItems="center" mb={1}>
                <Typography variant="subtitle2">Build Command *</Typography>
                <Tooltip title="The command to build your application (e.g., npm run build, yarn build)">
                  <IconButton size="small">
                    <HelpIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              </Box>
              <TextField
                fullWidth
                variant="outlined"
                size="small"
                value={config.buildCommand || ''}
                onChange={(e) => handleConfigChange('buildCommand', e.target.value)}
                error={!!getFieldError('buildCommand')}
                helperText={getFieldError('buildCommand')}
              />
            </Box>
            
            <Box mb={2}>
              <Box display="flex" alignItems="center" mb={1}>
                <Typography variant="subtitle2">Build Output Directory *</Typography>
                <Tooltip title="The directory where build artifacts are generated (e.g., build, dist)">
                  <IconButton size="small">
                    <HelpIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              </Box>
              <TextField
                fullWidth
                variant="outlined"
                size="small"
                value={config.buildPath || ''}
                onChange={(e) => handleConfigChange('buildPath', e.target.value)}
                error={!!getFieldError('buildPath')}
                helperText={getFieldError('buildPath')}
              />
            </Box>
            
            <Box mb={2}>
              <Box display="flex" alignItems="center" mb={1}>
                <Typography variant="subtitle2">Node.js Version</Typography>
                <Tooltip title="The Node.js version to use for building your application">
                  <IconButton size="small">
                    <HelpIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              </Box>
              <TextField
                fullWidth
                variant="outlined"
                size="small"
                value={config.nodeVersion || ''}
                onChange={(e) => handleConfigChange('nodeVersion', e.target.value)}
              />
            </Box>
          </>
        )}

        {/* Server application fields */}
        {['nodejs', 'dotnet', 'python', 'spring-boot', 'php', 'go', 'ruby', 'docker'].includes(preset.id) && (
          <>
            {preset.id !== 'docker' && (
              <Box mb={2}>
                <Box display="flex" alignItems="center" mb={1}>
                  <Typography variant="subtitle2">Language Version</Typography>
                  <Tooltip title={`The ${preset.name} version to use for your application`}>
                    <IconButton size="small">
                      <HelpIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                </Box>
                <TextField
                  fullWidth
                  variant="outlined"
                  size="small"
                  value={config.languageVersion || ''}
                  onChange={(e) => handleConfigChange('languageVersion', e.target.value)}
                />
              </Box>
            )}
            
            <Box mb={2}>
              <Box display="flex" alignItems="center" mb={1}>
                <Typography variant="subtitle2">Port *</Typography>
                <Tooltip title="The port number your application listens on">
                  <IconButton size="small">
                    <HelpIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              </Box>
              <TextField
                fullWidth
                variant="outlined"
                size="small"
                type="number"
                value={config.port || ''}
                onChange={(e) => handleConfigChange('port', parseInt(e.target.value) || undefined)}
                error={!!getFieldError('port')}
                helperText={getFieldError('port')}
              />
            </Box>
          </>
        )}

        {/* Python specific fields */}
        {preset.id === 'python' && (
          <Box mb={2}>
            <Box display="flex" alignItems="center" mb={1}>
              <Typography variant="subtitle2">Run Command</Typography>
              <Tooltip title="The command to start your Python application (e.g., python app.py, gunicorn app:app)">
                <IconButton size="small">
                  <HelpIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Box>
            <TextField
              fullWidth
              variant="outlined"
              size="small"
              value={config.runCommand || ''}
              onChange={(e) => handleConfigChange('runCommand', e.target.value)}
            />
          </Box>
        )}

        {/* Docker specific fields */}
        {preset.id === 'docker' && (
          <Box mb={2}>
            <Box display="flex" alignItems="center" mb={1}>
              <Typography variant="subtitle2">Dockerfile Path</Typography>
              <Tooltip title="Path to your Dockerfile relative to the repository root">
                <IconButton size="small">
                  <HelpIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Box>
            <TextField
              fullWidth
              variant="outlined"
              size="small"
              value={config.dockerfilePath || ''}
              onChange={(e) => handleConfigChange('dockerfilePath', e.target.value)}
            />
          </Box>
        )}
      </Box>
    );
  };

  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        Build Configuration
      </Typography>
      <Typography variant="body2" color="textSecondary" paragraph>
        Select the build preset that matches your web application technology
      </Typography>

      <FormControl component="fieldset" error={!!getFieldError('buildPreset')}>
        <FormLabel component="legend">Build Preset *</FormLabel>
        <RadioGroup
          value={data.buildPreset || ''}
          onChange={handlePresetChange}
        >
          <Grid container spacing={2}>
            {BUILD_PRESETS.map((preset) => (
              <Grid item xs={12} sm={6} md={4} key={preset.id}>
                <Card 
                  variant="outlined" 
                  style={{ 
                    cursor: 'pointer',
                    border: data.buildPreset === preset.id ? '2px solid #1976d2' : undefined 
                  }}
                  onClick={() => handlePresetChange({ target: { value: preset.id } } as any)}
                >
                  <CardContent style={{ padding: '12px' }}>
                    <FormControlLabel
                      value={preset.id}
                      control={<Radio />}
                      label={
                        <Box>
                          <Typography variant="subtitle2">{preset.name}</Typography>
                          <Typography variant="caption" color="textSecondary">
                            {preset.description}
                          </Typography>
                        </Box>
                      }
                      style={{ margin: 0, width: '100%' }}
                    />
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </RadioGroup>
        {getFieldError('buildPreset') && (
          <FormHelperText>{getFieldError('buildPreset')}</FormHelperText>
        )}
      </FormControl>

      {renderConfigFields()}
    </Box>
  );
};
