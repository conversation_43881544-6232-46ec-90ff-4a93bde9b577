import React, { useEffect } from 'react';
import {
  Box,
  <PERSON>Field,
  Typography,
  FormHelperText,
  Tooltip,
  IconButton,
} from '@material-ui/core';
import { Help as HelpIcon } from '@material-ui/icons';
import { WebAppCreationData } from '../../api';
import { generateComponentName, ValidationError } from './validation';

interface Step2ComponentDetailsProps {
  data: Partial<WebAppCreationData>;
  onChange: (field: keyof WebAppCreationData, value: string) => void;
  errors: ValidationError[];
}

export const Step2ComponentDetails: React.FC<Step2ComponentDetailsProps> = ({
  data,
  onChange,
  errors,
}) => {
  const getFieldError = (field: string): string | undefined => {
    return errors.find(error => error.field === field)?.message;
  };

  const handleDisplayNameChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    onChange('displayName', value);
    
    // Auto-generate component name if it hasn't been manually edited
    if (value && (!data.componentName || data.componentName === generateComponentName(data.displayName || ''))) {
      const generatedName = generateComponentName(value);
      onChange('componentName', generatedName);
    }
  };

  // Auto-generate component name on initial load if display name exists
  useEffect(() => {
    if (data.displayName && !data.componentName) {
      const generatedName = generateComponentName(data.displayName);
      onChange('componentName', generatedName);
    }
  }, [data.displayName, data.componentName, onChange]);

  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        Component Details
      </Typography>
      <Typography variant="body2" color="textSecondary" paragraph>
        Provide details about your web application component
      </Typography>

      <Box mb={3}>
        <Box display="flex" alignItems="center" mb={1}>
          <Typography variant="subtitle1" component="label">
            Display Name *
          </Typography>
          <Tooltip title="A human-readable name for your component that will be shown in the Choreo Console">
            <IconButton size="small" style={{ marginLeft: 4 }}>
              <HelpIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        </Box>
        <TextField
          fullWidth
          variant="outlined"
          placeholder="My React App"
          value={data.displayName || ''}
          onChange={handleDisplayNameChange}
          error={!!getFieldError('displayName')}
          helperText={getFieldError('displayName')}
        />
        <FormHelperText>
          This name will be displayed in the Choreo Console
        </FormHelperText>
      </Box>

      <Box mb={3}>
        <Box display="flex" alignItems="center" mb={1}>
          <Typography variant="subtitle1" component="label">
            Component Name *
          </Typography>
          <Tooltip title="A unique identifier for your component. This will be used in URLs and API calls. Only alphanumeric characters, hyphens, and underscores are allowed">
            <IconButton size="small" style={{ marginLeft: 4 }}>
              <HelpIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        </Box>
        <TextField
          fullWidth
          variant="outlined"
          placeholder="my-react-app"
          value={data.componentName || ''}
          onChange={(e) => onChange('componentName', e.target.value)}
          error={!!getFieldError('componentName')}
          helperText={getFieldError('componentName') || 'Auto-generated from display name, but you can customize it'}
        />
        <FormHelperText>
          Must be unique within the project. Only alphanumeric characters, hyphens, and underscores allowed
        </FormHelperText>
      </Box>

      <Box mb={3}>
        <Box display="flex" alignItems="center" mb={1}>
          <Typography variant="subtitle1" component="label">
            Description
          </Typography>
          <Tooltip title="Optional description of what your web application does">
            <IconButton size="small" style={{ marginLeft: 4 }}>
              <HelpIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        </Box>
        <TextField
          fullWidth
          variant="outlined"
          multiline
          rows={3}
          placeholder="A brief description of your web application..."
          value={data.description || ''}
          onChange={(e) => onChange('description', e.target.value)}
          error={!!getFieldError('description')}
          helperText={getFieldError('description')}
        />
        <FormHelperText>
          Optional. Maximum 200 characters
        </FormHelperText>
      </Box>
    </Box>
  );
};
