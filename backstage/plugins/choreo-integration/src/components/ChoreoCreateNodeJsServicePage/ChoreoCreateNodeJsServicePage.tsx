import React, { useState, useCallback } from 'react';
import { usePara<PERSON>, useNavigate, useLocation } from 'react-router-dom';
import {
  Typography,
  Box,
  IconButton,
  Paper,
  Stepper,
  Step,
  StepLabel,
  Button,
  CircularProgress,
  Snackbar,
} from '@material-ui/core';
import { Alert } from '@material-ui/lab';
import { ArrowBack as ArrowBackIcon } from '@material-ui/icons';
import { useRouteRef, useApi } from '@backstage/core-plugin-api';
import { createComponentRouteRef, componentsRouteRef } from '../../routes';
import { choreoApiRef, WebAppCreationData, WebAppBuildConfig } from '../../api';
import { Step1RepositoryConfig } from '../ChoreoCreateWebAppPage/Step1RepositoryConfig';
import { Step2ComponentDetails } from '../ChoreoCreateWebAppPage/Step2ComponentDetails';
import { Step3BuildConfig } from '../ChoreoCreateWebAppPage/Step3BuildConfig';
import { Step4ReviewDeploy } from '../ChoreoCreateWebAppPage/Step4ReviewDeploy';
import { getDefaultPreset } from '../ChoreoCreateWebAppPage/buildPresets';
import {
  validateStep1,
  validateStep2,
  validateStep3,
  validateAllSteps,
  ValidationError
} from '../ChoreoCreateWebAppPage/validation';

const STEPS = [
  'Repository Configuration',
  'Component Details',
  'Build Configuration',
  'Review & Deploy'
];

export const ChoreoCreateNodeJsServicePage = () => {
  const { orgHandler, projectId } = useParams<{
    orgHandler: string;
    projectId: string;
  }>();
  const navigate = useNavigate();
  const location = useLocation();
  const createComponentRoute = useRouteRef(createComponentRouteRef);
  const componentsRoute = useRouteRef(componentsRouteRef);
  const choreoApi = useApi(choreoApiRef);

  // Extract orgId from location state if available
  const orgId = location.state?.orgId || 0;

  // Wizard state
  const [activeStep, setActiveStep] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<ValidationError[]>([]);
  const [successMessage, setSuccessMessage] = useState<string>('');
  const [errorMessage, setErrorMessage] = useState<string>('');

  // Form data state
  const [formData, setFormData] = useState<Partial<WebAppCreationData>>({
    repositoryUrl: '',
    branch: 'main',
    componentDirectory: '',
    displayName: '',
    componentName: '',
    description: '',
    buildPreset: 'nodejs',
    buildConfig: getDefaultPreset().defaultConfig,
    orgId: orgId, // Set orgId from location state
    orgHandler: orgHandler || '',
    projectId: projectId || '',
  });

  const handleBackToCreate = () => {
    if (orgHandler && projectId) {
      navigate(createComponentRoute({ orgHandler, projectId }));
    }
  };

  const handleFieldChange = useCallback((field: keyof WebAppCreationData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));

    // Clear field-specific errors when user starts typing
    setErrors(prev => prev.filter(error => error.field !== field));
  }, []);

  const handleBuildConfigChange = useCallback((config: Partial<WebAppBuildConfig>) => {
    setFormData(prev => ({
      ...prev,
      buildConfig: {
        ...prev.buildConfig,
        ...config
      }
    }));

    // Clear build config related errors
    setErrors(prev => prev.filter(error =>
      !['buildCommand', 'buildPath', 'port', 'runCommand', 'dockerfilePath'].includes(error.field)
    ));
  }, []);

  const validateCurrentStep = (): boolean => {
    let validationResult;

    switch (activeStep) {
      case 0:
        validationResult = validateStep1(formData);
        break;
      case 1:
        validationResult = validateStep2(formData);
        break;
      case 2:
        validationResult = validateStep3(formData);
        break;
      case 3:
        validationResult = validateAllSteps(formData as WebAppCreationData);
        break;
      default:
        return true;
    }

    setErrors(validationResult.errors);
    return validationResult.isValid;
  };

  const handleNext = () => {
    if (validateCurrentStep()) {
      setActiveStep(prev => prev + 1);
    }
  };

  const handleBack = () => {
    setActiveStep(prev => prev - 1);
    setErrors([]); // Clear errors when going back
  };

  const handleCreateComponent = async () => {
    if (!validateCurrentStep()) {
      return;
    }

    setIsLoading(true);
    setErrorMessage('');

    try {
      // @ts-ignore
      const response = await choreoApi.createNodeJsServiceComponent({data: formData as WebAppCreationData});

      setSuccessMessage(`NodeJS service "${formData.displayName}" created successfully!`);

      // Navigate to components page after a short delay
      setTimeout(() => {
        if (orgHandler && projectId) {
          navigate(componentsRoute({ orgHandler, projectId }));
        }
      }, 2000);

    } catch (error) {
      console.error('Failed to create NodeJS service component:', error);
      setErrorMessage(
        error instanceof Error
          ? error.message
          : 'Failed to create NodeJS service. Please try again.'
      );
    } finally {
      setIsLoading(false);
    }
  };

  const renderStepContent = () => {
    switch (activeStep) {
      case 0:
        return (
          <Step1RepositoryConfig
            data={formData}
            onChange={handleFieldChange}
            errors={errors}
          />
        );
      case 1:
        return (
          <Step2ComponentDetails
            data={formData}
            onChange={handleFieldChange}
            errors={errors}
          />
        );
      case 2:
        return (
          <Step3BuildConfig
            data={formData}
            onChange={handleFieldChange}
            onBuildConfigChange={handleBuildConfigChange}
            errors={errors}
          />
        );
      case 3:
        return (
          <Step4ReviewDeploy
            data={formData as WebAppCreationData}
            orgHandler={orgHandler || ''}
            projectId={projectId || ''}
          />
        );
      default:
        return null;
    }
  };

  return (
    <Box p={3}>
      <Box mb={3} display="flex" alignItems="center">
        <IconButton
          onClick={handleBackToCreate}
          style={{ marginRight: 16 }}
          aria-label="back to create component"
        >
          <ArrowBackIcon />
        </IconButton>
        <Box>
          <Typography variant="h4" gutterBottom>
            Create a NodeJS Service
          </Typography>
          <Typography variant="body1" color="textSecondary">
            Configure your new NodeJS service component
          </Typography>
        </Box>
      </Box>

      <Paper style={{ padding: 24 }}>
        {/* Stepper */}
        <Stepper activeStep={activeStep} alternativeLabel style={{ marginBottom: 32 }}>
          {STEPS.map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>

        {/* Step Content */}
        <Box minHeight={400}>
          {renderStepContent()}
        </Box>

        {/* Navigation Buttons */}
        <Box display="flex" justifyContent="space-between" mt={4}>
          <Button
            onClick={activeStep === 0 ? handleBackToCreate : handleBack}
            disabled={isLoading}
          >
            {activeStep === 0 ? 'Cancel' : 'Back'}
          </Button>

          <Box>
            {activeStep < STEPS.length - 1 ? (
              <Button
                variant="contained"
                color="primary"
                onClick={handleNext}
                disabled={isLoading}
              >
                Next
              </Button>
            ) : (
              <Button
                variant="contained"
                color="primary"
                onClick={handleCreateComponent}
                disabled={isLoading}
                startIcon={isLoading ? <CircularProgress size={20} /> : undefined}
              >
                {isLoading ? 'Creating...' : 'Create and Deploy'}
              </Button>
            )}
          </Box>
        </Box>

        {/* Error Display */}
        {errors.length > 0 && (
          <Box mt={2}>
            <Alert severity="error">
              <Typography variant="subtitle2" gutterBottom>
                Please fix the following errors:
              </Typography>
              <ul style={{ margin: 0, paddingLeft: 20 }}>
                {errors.map((error, index) => (
                  <li key={index}>{error.message}</li>
                ))}
              </ul>
            </Alert>
          </Box>
        )}
      </Paper>

      {/* Success Snackbar */}
      <Snackbar
        open={!!successMessage}
        autoHideDuration={6000}
        onClose={() => setSuccessMessage('')}
      >
        <Alert severity="success" onClose={() => setSuccessMessage('')}>
          {successMessage}
        </Alert>
      </Snackbar>

      {/* Error Snackbar */}
      <Snackbar
        open={!!errorMessage}
        autoHideDuration={6000}
        onClose={() => setErrorMessage('')}
      >
        <Alert severity="error" onClose={() => setErrorMessage('')}>
          {errorMessage}
        </Alert>
      </Snackbar>
    </Box>
  );
};
