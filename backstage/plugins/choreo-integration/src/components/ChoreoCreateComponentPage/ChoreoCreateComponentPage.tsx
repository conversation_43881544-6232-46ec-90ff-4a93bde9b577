import React from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import {
  Typography,
  Box,
  Card,
  CardContent,
  CardActionArea,
  Grid,
  Tooltip,
  IconButton,
} from '@material-ui/core';
import { ArrowBack as ArrowBackIcon } from '@material-ui/icons';
import { useRouteRef } from '@backstage/core-plugin-api';
import { componentsRouteRef, createMicroserviceRouteRef, createWebAppRouteRef, createNodeJsServiceRouteRef } from '../../routes';

interface ComponentTypeCardProps {
  title: string;
  description: string;
  icon: string;
  isClickable: boolean;
  onClick?: () => void;
  tooltipMessage?: string;
}

const ComponentTypeCard: React.FC<ComponentTypeCardProps> = ({
  title,
  description,
  icon,
  isClickable,
  onClick,
  tooltipMessage,
}) => {
  const cardContent = (
    <Card
      style={{
        height: '100%',
        cursor: isClickable ? 'pointer' : 'default',
        opacity: isClickable ? 1 : 0.6,
      }}
    >
      <CardActionArea
        disabled={!isClickable}
        onClick={onClick}
        role="button"
        aria-label={`Create ${title.toLowerCase()}`}
      >
        <CardContent style={{ textAlign: 'center', padding: '24px' }}>
          <Box mb={2}>
            <Typography variant="h1" style={{ fontSize: '48px' }}>
              {icon}
            </Typography>
          </Box>
          <Typography variant="h6" gutterBottom>
            {title}
          </Typography>
          <Typography variant="body2" color="textSecondary">
            {description}
          </Typography>
        </CardContent>
      </CardActionArea>
    </Card>
  );

  if (!isClickable && tooltipMessage) {
    return (
      <Tooltip title={tooltipMessage} placement="top">
        {cardContent}
      </Tooltip>
    );
  }

  return cardContent;
};

export const ChoreoCreateComponentPage = () => {
  const { orgHandler, projectId } = useParams<{
    orgHandler: string;
    projectId: string;
  }>();
  const navigate = useNavigate();
  const componentsRoute = useRouteRef(componentsRouteRef);
  const microserviceRoute = useRouteRef(createMicroserviceRouteRef);
  const webAppRoute = useRouteRef(createWebAppRouteRef);
  const nodeJsServiceRoute = useRouteRef(createNodeJsServiceRouteRef);

  const handleBackToComponents = () => {
    if (orgHandler && projectId) {
      navigate(componentsRoute({ orgHandler, projectId }));
    }
  };

  const handleServiceClick = () => {
    if (orgHandler && projectId) {
      navigate(microserviceRoute({ orgHandler, projectId }));
    }
  };

  const handleWebAppClick = () => {
    if (orgHandler && projectId) {
      // Pass orgId to the web app creation page
      navigate(webAppRoute({ orgHandler, projectId }), {
        state: { orgId: 74800 } // Hardcoded orgId for now
      });
    }
  };

  const handleNodeJsServiceClick = () => {
    if (orgHandler && projectId) {
      navigate(nodeJsServiceRoute({ orgHandler, projectId }), {
        state: { orgId: 74800 } // Hardcoded orgId for now
      });
    }
  };

  const componentTypes = [
    {
      title: 'Service',
      description: 'Create a microservice or API',
      icon: '⚙️',
      isClickable: true,
      onClick: handleServiceClick,
    },
    {
      title: 'Web Application',
      description: 'Create a web application',
      icon: '🌐',
      isClickable: true,
      onClick: handleWebAppClick,
    },
    {
      title: 'NodeJS Service',
      description: 'Create a NodeJS service',
      icon: '🚀',
      isClickable: true,
      onClick: handleNodeJsServiceClick,
    },
    {
      title: 'Scheduled Task',
      description: 'Create a scheduled task or cron job',
      icon: '⏰',
      isClickable: false,
      tooltipMessage: 'To create this component please go to https://console.choreo.dev/',
    },
    {
      title: 'Manual Task',
      description: 'Create a manual task',
      icon: '📋',
      isClickable: false,
      tooltipMessage: 'To create this component please go to https://console.choreo.dev/',
    },
    {
      title: 'Test Runner',
      description: 'Create a test runner',
      icon: '🧪',
      isClickable: false,
      tooltipMessage: 'To create this component please go to https://console.choreo.dev/',
    },
    {
      title: 'Webhook',
      description: 'Create a webhook handler',
      icon: '🔗',
      isClickable: false,
      tooltipMessage: 'To create this component please go to https://console.choreo.dev/',
    },
    {
      title: 'MCP Server',
      description: 'Create an MCP server',
      icon: '🖥️',
      isClickable: false,
      tooltipMessage: 'To create this component please go to https://console.choreo.dev/',
    },
    {
      title: 'API Proxy',
      description: 'Create an API proxy',
      icon: '🔄',
      isClickable: false,
      tooltipMessage: 'To create this component please go to https://console.choreo.dev/',
    },
    {
      title: 'Event Handler',
      description: 'Create an event handler',
      icon: '⚡',
      isClickable: false,
      tooltipMessage: 'To create this component please go to https://console.choreo.dev/',
    },
    {
      title: 'External Consumer',
      description: 'Create an external consumer',
      icon: '📡',
      isClickable: false,
      tooltipMessage: 'To create this component please go to https://console.choreo.dev/',
    },
  ];

  return (
    <Box p={3}>
      <Box mb={3} display="flex" alignItems="center">
        <IconButton
          onClick={handleBackToComponents}
          style={{ marginRight: 16 }}
          aria-label="back to components"
        >
          <ArrowBackIcon />
        </IconButton>
        <Box>
          <Typography variant="h4" gutterBottom>
            Create a New Component
          </Typography>
          <Typography variant="body1" color="textSecondary">
            Choose the type of component you want to create in your project
          </Typography>
        </Box>
      </Box>

      <Grid container spacing={3}>
        {componentTypes.map((type, index) => (
          <Grid item xs={12} sm={6} md={4} key={index}>
            <ComponentTypeCard
              title={type.title}
              description={type.description}
              icon={type.icon}
              isClickable={type.isClickable}
              onClick={type.onClick}
              tooltipMessage={type.tooltipMessage}
            />
          </Grid>
        ))}
      </Grid>
    </Box>
  );
};
