import { createApiRef } from '@backstage/core-plugin-api';

export interface ChoreoOrg {
  id: string;
  uuid: string;
  handle: string;
  name: string;
  owner: {
    id: string;
    idpId: string;
    createdAt: string;
  };
}

export interface ChoreoOrgsResponse {
  organizations: ChoreoOrg[];
  status: string;
  count: number;
}

export interface ChoreoProject {
  id: string;
  orgId: string;
  name: string;
  version: string;
  createdDate: string;
  handler: string;
  region: string;
  description?: string;
  defaultDeploymentPipelineId?: string;
  deploymentPipelineIds?: string[];
  type?: string;
  gitProvider?: string;
  gitOrganization?: string;
  repository?: string;
  branch?: string;
  secretRef?: string;
  updatedAt: string;
}

export interface ChoreoProjectsResponse {
  projects: ChoreoProject[];
  status: string;
  count: number;
}

export interface ProjectCreationEligibility {
  isProjectCreationAllowed: boolean;
}

export interface ProjectCreationEligibilityResponse {
  projectCreationEligibility: ProjectCreationEligibility;
}

// Component-related interfaces
export interface ChoreoComponentRepository {
  buildpackConfig?: {
    versionId: string;
    buildContext: string;
    languageVersion: string;
    buildpack: {
      id: string;
      language: string;
    };
  };
  byocWebAppBuildConfig?: {
    id: string;
    dockerContext: string;
    webAppType: string;
  };
}

export interface ChoreoComponentApiVersion {
  apiVersion: string;
  proxyName: string;
  proxyUrl: string;
  proxyId: string;
  id: string;
  state: string;
  latest: boolean;
  branch: string;
  accessibility: string;
}

export interface ChoreoComponentDeploymentTrack {
  id: string;
  createdAt: string;
  updatedAt: string;
  apiVersion: string;
  branch: string;
  description: string;
  componentId: string;
  latest: boolean;
  versionStrategy: string;
  autoDeployEnabled: boolean;
}

export interface ChoreoComponent {
  projectId: string;
  id: string;
  description?: string;
  status: string;
  initStatus: string;
  name: string;
  handler: string;
  displayName: string;
  displayType: string;
  version: string;
  createdAt: string;
  lastBuildDate?: string;
  orgHandler: string;
  isSystemComponent: boolean;
  repository?: ChoreoComponentRepository;
  componentSubType: string;
  apiVersions?: ChoreoComponentApiVersion[];
  deploymentTracks?: ChoreoComponentDeploymentTrack[];
}

export interface ChoreoComponentsResponse {
  components: ChoreoComponent[];
}

// Environment-related interfaces
export interface ChoreoEnvironment {
  name: string;
  id: string;
  choreoEnv: string;
  vhost: string;
  apiEnvName: string;
  isMigrating: boolean;
  apimEnvId: string;
  namespace: string;
  sandboxVhost: string;
  critical: boolean;
  isPdp: boolean;
  promoteFrom?: string;
  dpId: string;
  templateId: string;
  scaleToZeroEnabled: boolean;
}

export interface ChoreoEnvironmentsResponse {
  environments: ChoreoEnvironment[];
}

export interface DefaultOrgSelectionOptions {
  returnToOrg?: string;
  selectedOrgHandle?: string;
  sessionStoredOrg?: string;
  userIdpId?: string;
}

export interface DefaultOrgResult {
  handle: string;
  uuid: string;
  organization: ChoreoOrg;
}

// Component creation interfaces
export interface WebAppBuildPreset {
  id: string;
  name: string;
  description: string;
  defaultConfig: WebAppBuildConfig;
}

export interface WebAppBuildConfig {
  buildCommand?: string;
  buildPath?: string;
  nodeVersion?: string;
  languageVersion?: string;
  port?: number;
  runCommand?: string;
  dockerfilePath?: string;
}

export interface WebAppCreationData {
  // Step 1: Repository Configuration
  repositoryUrl: string;
  branch: string;
  componentDirectory: string;

  // Step 2: Component Details
  displayName: string;
  componentName: string;
  description: string;

  // Step 3: Build Configuration
  buildPreset: string;
  buildConfig: WebAppBuildConfig;

  // Context
  orgId: number;
  orgHandler: string;
  projectId: string;
}

export interface NodeJsServiceCreationData {
  // Step 1: Repository Configuration
  repositoryUrl: string;
  branch: string;
  componentDirectory: string;

  // Step 2: Component Details
  displayName: string;
  componentName: string;
  description: string;

  // Step 3: Build Configuration
  buildPreset: string;
  buildConfig: WebAppBuildConfig;

  // Context
  orgId: number;
  orgHandler: string;
  projectId: string;
}

export interface ComponentCreationResponse {
  id: string;
  name: string;
  status: string;
  message?: string;
}

export interface ChoreoApi {
  /**
   * Fetches organization data from the Choreo backend
   */
  getOrganizations(): Promise<ChoreoOrgsResponse>;

  /**
   * Fetches project data from the Choreo backend using GraphQL
   */
  getProjects(orgId: number, orgHandler: string): Promise<ChoreoProjectsResponse>;

  /**
   * Checks if project creation is allowed for the organization
   */
  getProjectCreationEligibility(orgId: number, orgHandler: string): Promise<ProjectCreationEligibilityResponse>;

  /**
   * Fetches components for a specific project using GraphQL
   */
  getComponents(orgHandler: string, projectId: string): Promise<ChoreoComponentsResponse>;

  /**
   * Fetches environments for a specific project using GraphQL
   */
  getEnvironments(orgUuid: string, projectId: string, type?: string): Promise<ChoreoEnvironmentsResponse>;

  /**
   * Creates a new web application component
   */
  createWebAppComponent(data: WebAppCreationData): Promise<ComponentCreationResponse>;

  /**
   * Creates a new NodeJS service component
   */
  createNodeJsServiceComponent(data: NodeJsServiceCreationData): Promise<ComponentCreationResponse>;

  /**
   * Determines the default organization based on Choreo Console logic
   */
  getDefaultOrganization(
    organizations: ChoreoOrg[],
    options?: DefaultOrgSelectionOptions
  ): DefaultOrgResult | null;
}

export const choreoApiRef = createApiRef<ChoreoApi>({
  id: 'plugin.choreo-integration.service',
});
