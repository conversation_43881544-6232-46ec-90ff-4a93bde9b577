import {
  createPlugin,
  createRoutableExtension,
  createApiFactory,
  discoveryApiRef,
  fetchApiRef,
} from '@backstage/core-plugin-api';

import {
  rootRouteRef,
  componentsRouteRef,
  createComponentRouteRef,
  createMicroserviceRouteRef,
  createWebAppRouteRef,
  createNodeJsServiceRouteRef
} from './routes';
import { choreoApiRef, ChoreoClient } from './api';

export const choreoIntegrationPlugin = createPlugin({
  id: 'choreo-integration',
  routes: {
    root: rootRouteRef,
    components: componentsRouteRef,
    createComponent: createComponentRouteRef,
    createMicroservice: createMicroserviceRouteRef,
    createWebApp: createWebAppRouteRef,
    createNodeJsService: createNodeJsServiceRouteRef,
  },
  apis: [
    createApiFactory({
      api: choreoApiRef,
      deps: {
        discoveryApi: discoveryApiRef,
        fetchApi: fetchApiRef,
      },
      factory: ({ discoveryApi, fetchApi }) =>
        new ChoreoClient({ discoveryApi, fetchApi }),
    }),
  ],
});

export const ChoreoIntegrationPage = choreoIntegrationPlugin.provide(
  createRoutableExtension({
    name: 'ChoreoIntegrationPage',
    component: () =>
      import('./components/ExampleComponent').then(m => m.ExampleComponent),
    mountPoint: rootRouteRef,
  }),
);

export const ChoreoComponentsPage = choreoIntegrationPlugin.provide(
  createRoutableExtension({
    name: 'ChoreoComponentsPage',
    component: () =>
      import('./components/ChoreoComponentsPage').then(m => m.ChoreoComponentsPage),
    mountPoint: componentsRouteRef,
  }),
);

export const ChoreoCreateComponentPage = choreoIntegrationPlugin.provide(
  createRoutableExtension({
    name: 'ChoreoCreateComponentPage',
    component: () =>
      import('./components/ChoreoCreateComponentPage').then(m => m.ChoreoCreateComponentPage),
    mountPoint: createComponentRouteRef,
  }),
);

export const ChoreoCreateMicroservicePage = choreoIntegrationPlugin.provide(
  createRoutableExtension({
    name: 'ChoreoCreateMicroservicePage',
    component: () =>
      import('./components/ChoreoCreateMicroservicePage').then(m => m.ChoreoCreateMicroservicePage),
    mountPoint: createMicroserviceRouteRef,
  }),
);

export const ChoreoCreateWebAppPage = choreoIntegrationPlugin.provide(
  createRoutableExtension({
    name: 'ChoreoCreateWebAppPage',
    component: () =>
      import('./components/ChoreoCreateWebAppPage').then(m => m.ChoreoCreateWebAppPage),
    mountPoint: createWebAppRouteRef,
  }),
);

export const ChoreoCreateNodeJsServicePage = choreoIntegrationPlugin.provide(
  createRoutableExtension({
    name: 'ChoreoCreateNodeJsServicePage',
    component: () =>
      import('./components/ChoreoCreateNodeJsServicePage/ChoreoCreateNodeJsServicePage').then(m => m.ChoreoCreateNodeJsServicePage),
    mountPoint: createNodeJsServiceRouteRef,
  }),
);
